/**
 * Página standalone para el Mejorador de Imagen
 * Permite mejorar la resolución y calidad de imágenes usando Stability AI
 */
import React, { useCallback, useState } from "react";
import { useLocation } from "wouter";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Slider } from "@/components/ui/slider";
import { Progress } from "@/components/ui/progress";
import {
  ArrowLeft,
  Download,
  ImagePlus,
  Sparkles,
  Upload,
  Heart,
  HeartOff,
  Copy,
  Zap,
  ArrowUpRight,
  Settings,
  Crown,
  Cpu,
  Info,
  Wand2,
  RefreshCw,
  Share2,
  Eye,
  Trash2
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { motion } from "framer-motion";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

// Interfaces para los modos de upscale
interface UpscaleModeInfo {
  id: string;
  name: string;
  description: string;
  credits: number;
  max_resolution: string;
  requires_prompt: boolean;
  processing_time: string;
}

interface StylePresetInfo {
  id: string;
  name: string;
}

// Función para traducir prompts al inglés (copiada de ai-image-editor)
const translatePromptToEnglish = (spanishPrompt: string): string => {
  const translations: Record<string, string> = {
    // Términos comunes
    'persona': 'person',
    'personas': 'people',
    'hombre': 'man',
    'mujer': 'woman',
    'niño': 'child',
    'niña': 'girl',
    'retrato': 'portrait',
    'cara': 'face',
    'rostro': 'face',
    'sonrisa': 'smile',
    'ojos': 'eyes',
    'cabello': 'hair',
    'pelo': 'hair',

    // Paisajes y lugares
    'paisaje': 'landscape',
    'montaña': 'mountain',
    'montañas': 'mountains',
    'mar': 'sea',
    'océano': 'ocean',
    'playa': 'beach',
    'bosque': 'forest',
    'árbol': 'tree',
    'árboles': 'trees',
    'cielo': 'sky',
    'nubes': 'clouds',
    'sol': 'sun',
    'luna': 'moon',
    'estrella': 'star',
    'estrellas': 'stars',
    'ciudad': 'city',
    'edificio': 'building',
    'edificios': 'buildings',
    'casa': 'house',
    'calle': 'street',
    'carretera': 'road',
    'camino': 'path',

    // Animales
    'perro': 'dog',
    'gato': 'cat',
    'pájaro': 'bird',
    'caballo': 'horse',
    'león': 'lion',
    'tigre': 'tiger',
    'elefante': 'elephant',
    'oso': 'bear',
    'lobo': 'wolf',
    'águila': 'eagle',
    'pez': 'fish',
    'mariposa': 'butterfly',

    // Objetos
    'coche': 'car',
    'auto': 'car',
    'automóvil': 'car',
    'bicicleta': 'bicycle',
    'moto': 'motorcycle',
    'avión': 'airplane',
    'barco': 'boat',
    'tren': 'train',
    'mesa': 'table',
    'silla': 'chair',
    'libro': 'book',
    'teléfono': 'phone',
    'computadora': 'computer',
    'ordenador': 'computer',
    'cámara': 'camera',
    'reloj': 'watch',
    'flor': 'flower',
    'flores': 'flowers',

    // Colores
    'rojo': 'red',
    'azul': 'blue',
    'verde': 'green',
    'amarillo': 'yellow',
    'negro': 'black',
    'blanco': 'white',
    'gris': 'gray',
    'rosa': 'pink',
    'morado': 'purple',
    'naranja': 'orange',
    'marrón': 'brown',
    'dorado': 'golden',
    'plateado': 'silver',

    // Adjetivos
    'grande': 'big',
    'pequeño': 'small',
    'alto': 'tall',
    'bajo': 'short',
    'largo': 'long',
    'corto': 'short',
    'nuevo': 'new',
    'viejo': 'old',
    'joven': 'young',
    'hermoso': 'beautiful',
    'bonito': 'beautiful',
    'feo': 'ugly',
    'bueno': 'good',
    'malo': 'bad',
    'rápido': 'fast',
    'lento': 'slow',
    'fuerte': 'strong',
    'débil': 'weak',
    'caliente': 'hot',
    'frío': 'cold',
    'brillante': 'bright',
    'oscuro': 'dark',
    'claro': 'light',
    'limpio': 'clean',
    'sucio': 'dirty',
    'moderno': 'modern',
    'antiguo': 'ancient',
    'natural': 'natural',
    'artificial': 'artificial',

    // Calidad y estilo
    'alta calidad': 'high quality',
    'buena calidad': 'good quality',
    'profesional': 'professional',
    'detallado': 'detailed',
    'realista': 'realistic',
    'artístico': 'artistic',
    'elegante': 'elegant',
    'simple': 'simple',
    'complejo': 'complex',
    'minimalista': 'minimalist',
    'colorido': 'colorful',
    'vibrante': 'vibrant',
    'suave': 'soft',
    'nítido': 'sharp',
    'borroso': 'blurry',
    'enfocado': 'focused',
    'desenfocado': 'unfocused',
  };

  let translatedPrompt = spanishPrompt.toLowerCase();

  // Aplicar traducciones
  Object.entries(translations).forEach(([spanish, english]) => {
    const regex = new RegExp(`\\b${spanish}\\b`, 'gi');
    translatedPrompt = translatedPrompt.replace(regex, english);
  });

  return translatedPrompt;
};

// Interfaz para imágenes guardadas
interface SavedImage {
  id: string;
  originalUrl: string;
  processedUrl: string;
  originalFilename: string;
  outputFormat: string;
  timestamp: number;
  isFavorite: boolean;
  mode: string;
}

// Custom hook para localStorage
const useLocalStorage = <T,>(key: string, initialValue: T): [T, (value: T | ((val: T) => T)) => void] => {
  const [storedValue, setStoredValue] = useState<T>(() => {
    if (typeof window === "undefined") {
      return initialValue;
    }

    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.log(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  const setValue = (value: T | ((val: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);

      if (typeof window !== "undefined") {
        window.localStorage.setItem(key, JSON.stringify(valueToStore));
        window.dispatchEvent(new CustomEvent('localStorage', {
          detail: { key, newValue: valueToStore }
        }));
      }
    } catch (error) {
      console.log(`Error setting localStorage key "${key}":`, error);
    }
  };

  return [storedValue, setValue];
};

// Funciones para manejar imágenes guardadas
const SAVED_IMAGES_KEY = 'emma_saved_enhanced_images';

const createSavedImage = (imageData: Omit<SavedImage, 'id' | 'timestamp' | 'isFavorite'>): SavedImage => {
  const timestamp = Date.now();
  const randomPart = Math.floor(Math.random() * 1000000).toString();

  return {
    ...imageData,
    id: `enhanced_img_${timestamp}_${randomPart}`,
    timestamp: timestamp,
    isFavorite: true,
  };
};

const isImageSaved = (imageUrl: string, savedImages: SavedImage[]): boolean => {
  return savedImages.some(img => img.processedUrl === imageUrl);
};

export default function ImageEnhancerPage() {
  const [_, setLocation] = useLocation();
  const { toast } = useToast();

  const navigate = (path: string) => {
    setLocation(path);
  };

  // Estados principales
  const [originalImage, setOriginalImage] = useState<File | null>(null);
  const [originalImagePreview, setOriginalImagePreview] = useState<string | null>(null);
  const [resultImage, setResultImage] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [originalName, setOriginalName] = useState<string>("");
  const [progress, setProgress] = useState(0);
  const [progressMessage, setProgressMessage] = useState("");

  // Estados de configuración
  const [mode, setMode] = useState<"fast" | "conservative" | "creative">("fast");
  const [prompt, setPrompt] = useState("");
  const [negativePrompt, setNegativePrompt] = useState("");
  const [seed, setSeed] = useState<number | undefined>(undefined);
  const [creativity, setCreativity] = useState([0.3]);
  const [stylePreset, setStylePreset] = useState<string>("");
  const [outputFormat, setOutputFormat] = useState<"jpeg" | "png" | "webp">("webp");

  // Generar seed aleatorio automáticamente para ciertos modos
  React.useEffect(() => {
    if (mode === "fast" || mode === "conservative" || mode === "creative") {
      const randomSeed = Math.floor(Math.random() * 4294967294);
      setSeed(randomSeed);
    }
  }, [mode]);

  // Estados para la funcionalidad de guardados
  const [savedImages, setSavedImages] = useLocalStorage<SavedImage[]>(SAVED_IMAGES_KEY, []);
  const [currentImageSaved, setCurrentImageSaved] = useState(false);
  const [mainTab, setMainTab] = useState<"latest" | "saved">("latest");

  // Datos estáticos de modos (para evitar problemas de carga)
  const modes: UpscaleModeInfo[] = [
    {
      id: "fast",
      name: "Rápido",
      description: "Mejora 4x la resolución en ~1 segundo",
      credits: 1,
      max_resolution: "4x original",
      requires_prompt: false,
      processing_time: "~1 segundo"
    },
    {
      id: "conservative",
      name: "Conservador",
      description: "Mejora hasta 4K preservando aspectos originales",
      credits: 25,
      max_resolution: "4K",
      requires_prompt: true,
      processing_time: "~30-60 segundos"
    },
    {
      id: "creative",
      name: "Creativo",
      description: "Mejora hasta 4K reimaginando la imagen",
      credits: 25,
      max_resolution: "4K",
      requires_prompt: true,
      processing_time: "~2-5 minutos"
    }
  ];

  const stylePresets: StylePresetInfo[] = [
    { id: "photographic", name: "Fotográfico" },
    { id: "digital-art", name: "Arte Digital" },
    { id: "comic-book", name: "Cómic" },
    { id: "fantasy-art", name: "Arte Fantástico" },
    { id: "line-art", name: "Arte Lineal" },
    { id: "analog-film", name: "Película Analógica" },
    { id: "neon-punk", name: "Neon Punk" },
    { id: "isometric", name: "Isométrico" },
    { id: "low-poly", name: "Low Poly" },
    { id: "origami", name: "Origami" },
    { id: "modeling-compound", name: "Plastilina" },
    { id: "cinematic", name: "Cinematográfico" },
    { id: "3d-model", name: "Modelo 3D" },
    { id: "pixel-art", name: "Pixel Art" }
  ];

  // Información del modo actual
  const currentModeInfo = modes.find(m => m.id === mode);



  // Verificar si la imagen actual está guardada
  React.useEffect(() => {
    if (resultImage) {
      setCurrentImageSaved(isImageSaved(resultImage, savedImages));
    }
  }, [resultImage, savedImages]);

  // Manejar la selección de imagen
  const handleImageSelect = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (!file) return;

      // Validar que sea una imagen
      if (!file.type.startsWith("image/")) {
        toast({
          title: "Formato no soportado",
          description: "Por favor, selecciona un archivo de imagen válido.",
          variant: "destructive",
        });
        return;
      }

      // Guardar el nombre original
      setOriginalName(file.name);

      // Crear URL para previsualización
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setOriginalImagePreview(result);
        setOriginalImage(file);
        setResultImage(null); // Limpiar resultado anterior
      };
      reader.readAsDataURL(file);

      toast({
        title: "Imagen cargada",
        description: "Ahora puedes mejorar su calidad con IA.",
      });
    },
    [toast],
  );

  // Función para polling de resultados creativos (copiada de ai-image-editor)
  const pollCreativeUpscaleResult = async (generationId: string) => {
    const maxAttempts = 30; // 5 minutos máximo
    const pollInterval = 10000; // 10 segundos

    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      try {
        await new Promise(resolve => setTimeout(resolve, pollInterval));

        const response = await fetch(`/api/v1/images/enhance-image/creative/result/${generationId}`);

        if (response.status === 202) {
          // Aún procesando
          const progress = Math.min(20 + (attempt / maxAttempts) * 70, 90);
          setProgress(progress);
          setProgressMessage(`Procesando con IA... (${attempt + 1}/${maxAttempts})`);
          continue;
        }

        if (!response.ok) {
          throw new Error(`Error ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();

        if (result.success && result.url) {
          setResultImage(result.url);
          setProgress(100);
          setProgressMessage("¡Imagen mejorada exitosamente!");
          toast({
            title: "🎉 ¡Calidad mejorada!",
            description: "Imagen procesada exitosamente con modo creativo",
          });
          return;
        } else {
          throw new Error(result.error || 'Error en el procesamiento creativo');
        }
      } catch (error) {
        console.error(`Error en intento ${attempt + 1}:`, error);
        if (attempt === maxAttempts - 1) {
          throw new Error('Tiempo de espera agotado. Intenta de nuevo.');
        }
      }
    }
  };

  // Procesar la imagen (usando el mismo patrón que ai-image-editor)
  const handleProcessImage = useCallback(async () => {
    if (!originalImage) {
      toast({
        title: "Faltan datos",
        description: "Por favor, selecciona una imagen primero.",
        variant: "destructive",
      });
      return;
    }

    // Validar prompt para modos que lo requieren
    if ((mode === "conservative" || mode === "creative") && !prompt.trim()) {
      toast({
        title: "❌ Prompt requerido",
        description: `El modo ${mode === "conservative" ? "Conservador" : "Creativo"} requiere una descripción`,
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);
    setProgress(0);
    setProgressMessage("Iniciando mejora de calidad...");
    setResultImage(null);

    try {
      // Crear FormData (igual que en ai-image-editor)
      const formData = new FormData();
      formData.append('image', originalImage);
      formData.append('upscale_type', mode);
      formData.append('output_format', outputFormat);

      if (mode !== "fast") {
        // Traducir el prompt al inglés para Stability AI
        const originalPrompt = prompt.trim() || 'high quality image';
        console.log(`🔄 Traduciendo prompt: "${originalPrompt}"`);
        const translatedPrompt = translatePromptToEnglish(originalPrompt);
        console.log(`✅ Prompt traducido: "${translatedPrompt}"`);

        formData.append('prompt', translatedPrompt);
      }

      formData.append('creativity', creativity[0].toString());

      if (mode === "creative" && stylePreset) {
        formData.append('style_preset', stylePreset);
      }

      setProgress(25);
      setProgressMessage("Enviando imagen a la IA...");

      // Llamar a la API (igual que en ai-image-editor)
      const response = await fetch('/api/v1/images/enhance-image', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Error al procesar la imagen');
      }

      const result = await response.json();

      if (result.success) {
        if (mode === "creative" && result.metadata?.generation_id) {
          // Para creative upscale, iniciar polling
          setProgress(50);
          setProgressMessage("Procesando con IA creativa...");
          await pollCreativeUpscaleResult(result.metadata.generation_id);
        } else if (result.url) {
          // Para fast y conservative, resultado inmediato
          setResultImage(result.url);
          toast({
            title: "🎉 ¡Calidad mejorada!",
            description: `Imagen procesada exitosamente con modo ${currentModeInfo?.name || mode}`,
          });
        }
      } else {
        throw new Error(result.error || 'Error desconocido');
      }

    } catch (error) {
      console.error("Error en upscale:", error);
      toast({
        title: "❌ Error al mejorar",
        description: error instanceof Error ? error.message : "Error desconocido",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
      setProgress(0);
      setProgressMessage("");
    }
  }, [originalImage, mode, prompt, negativePrompt, seed, outputFormat, creativity, stylePreset, currentModeInfo, toast]);

  // Manejar descarga
  const handleDownload = useCallback(() => {
    if (!resultImage) return;

    const link = document.createElement("a");
    link.href = resultImage;
    link.download = `${originalName.replace(/\.[^/.]+$/, "")}_enhanced.${outputFormat}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast({
      title: "📥 Descarga iniciada",
      description: "La imagen mejorada se está descargando",
    });
  }, [resultImage, originalName, outputFormat, toast]);

  // Manejar copia al portapapeles
  const handleShare = useCallback(async () => {
    if (!resultImage) return;

    try {
      const response = await fetch(resultImage);
      const blob = await response.blob();

      await navigator.clipboard.write([
        new ClipboardItem({ [blob.type]: blob })
      ]);

      toast({
        title: "📋 ¡Copiada!",
        description: "Imagen copiada al portapapeles exitosamente",
      });
    } catch (error) {
      console.error("Error copiando al portapapeles:", error);
      toast({
        title: "❌ Error al copiar",
        description: "No se pudo copiar la imagen al portapapeles",
        variant: "destructive",
      });
    }
  }, [resultImage, toast]);

  // Función para manejar favoritos
  const handleToggleFavorite = useCallback(() => {
    if (!resultImage || !originalImagePreview) return;

    try {
      if (currentImageSaved) {
        // Quitar de favoritos
        const savedImage = savedImages.find(img => img.processedUrl === resultImage);
        if (savedImage) {
          const filteredImages = savedImages.filter(img => img.id !== savedImage.id);
          setSavedImages(filteredImages);
          setCurrentImageSaved(false);

          toast({
            title: "💔 Eliminada de favoritos",
            description: "La imagen ha sido eliminada de tus favoritos.",
          });
        }
      } else {
        // Agregar a favoritos
        const imageData = {
          originalUrl: originalImagePreview,
          processedUrl: resultImage,
          originalFilename: originalName || "imagen",
          outputFormat,
          mode: currentModeInfo?.name || mode,
        };

        const newImage = createSavedImage(imageData);
        const updatedImages = [newImage, ...savedImages].slice(0, 50); // Limitar a 50

        setSavedImages(updatedImages);
        setCurrentImageSaved(true);

        toast({
          title: "❤️ ¡Guardada en favoritos!",
          description: "Imagen guardada exitosamente en tus favoritos.",
        });
      }
    } catch (error) {
      console.error('Error al manejar favoritos:', error);

      toast({
        title: "❌ Error",
        description: "No se pudo guardar la imagen. Intenta de nuevo.",
        variant: "destructive",
      });
    }
  }, [resultImage, originalImagePreview, originalName, outputFormat, mode, currentModeInfo, currentImageSaved, savedImages, setSavedImages, toast]);

  // Verificar si la imagen actual está guardada
  React.useEffect(() => {
    if (resultImage) {
      setCurrentImageSaved(isImageSaved(resultImage, savedImages));
    }
  }, [resultImage, savedImages]);

  return (
    <DashboardLayout pageTitle="Mejorador de Imagen">
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-indigo-50/30 p-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="relative bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 rounded-2xl p-8 mb-8 overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-indigo-500/20 to-purple-500/20"></div>
            <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-white/10 to-transparent rounded-full -translate-y-32 translate-x-32"></div>

            <div className="relative z-10">
              <div className="flex items-center gap-4 mb-4">
                <div className="p-3 bg-white/20 backdrop-blur-sm rounded-xl">
                  <Sparkles className="h-8 w-8 text-white" />
                </div>
                <h1 className="text-4xl font-bold text-white">
                  Mejorador de Imagen
                </h1>
              </div>
              <p className="text-xl text-indigo-100 mb-6 max-w-3xl">
                Mejora la resolución y calidad de tus imágenes con IA avanzada. Desde mejoras rápidas hasta transformaciones creativas.
              </p>
              <div className="flex flex-wrap gap-2">
                <Badge className="bg-white/20 text-white border-white/30">
                  <Zap className="w-3 h-3 mr-1" />
                  Mejora rápida
                </Badge>
                <Badge className="bg-white/20 text-white border-white/30">
                  <Sparkles className="w-3 h-3 mr-1" />
                  Hasta 4K
                </Badge>
                <Badge className="bg-white/20 text-white border-white/30">
                  <Wand2 className="w-3 h-3 mr-1" />
                  Modo creativo
                </Badge>
              </div>
            </div>
          </div>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Panel de Control */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.1 }}
            className="lg:col-span-1"
          >
            <Card className="sticky top-6 shadow-xl border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2 text-xl">
                  <Settings className="h-5 w-5 text-indigo-600" />
                  Panel de Control
                </CardTitle>
                <CardDescription>
                  Configura y mejora tu imagen
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Selector de modo */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium">Modo de Mejora</Label>
                    <div className="grid grid-cols-1 gap-2">
                      {modes.map((modeInfo) => (
                        <Button
                          key={modeInfo.id}
                          variant={mode === modeInfo.id ? "default" : "outline"}
                          size="sm"
                          onClick={() => setMode(modeInfo.id as "fast" | "conservative" | "creative")}
                          className={`justify-start transition-all ${
                            mode === modeInfo.id
                              ? "bg-indigo-600 hover:bg-indigo-700 text-white shadow-md"
                              : "hover:bg-indigo-50 hover:border-indigo-300"
                          }`}
                        >
                          <div className="text-left w-full">
                            <div className="font-medium">{modeInfo.name}</div>
                            <div className="text-xs opacity-75">
                              {modeInfo.description}
                            </div>
                          </div>
                          {mode === modeInfo.id && (
                            <div className="ml-auto">
                              <div className="w-2 h-2 bg-white rounded-full"></div>
                            </div>
                          )}
                        </Button>
                      ))}
                    </div>
                  </div>

                  {/* Cargar imagen */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium">Cargar Imagen</Label>
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                      <input
                        type="file"
                        accept="image/*"
                        onChange={handleImageSelect}
                        className="hidden"
                        id="image-upload"
                      />
                      <Button
                        variant="outline"
                        onClick={() => document.getElementById('image-upload')?.click()}
                        className="mb-2"
                      >
                        <Upload className="h-4 w-4 mr-2" />
                        {originalImage ? 'Cambiar Imagen' : 'Subir Imagen'}
                      </Button>
                      <p className="text-xs text-gray-500">
                        PNG, JPG, WebP hasta 10MB
                      </p>
                    </div>

                    {/* Mostrar imagen cargada */}
                    {originalImage && originalImagePreview && (
                      <div className="relative group">
                        <img
                          src={originalImagePreview}
                          alt="Imagen original"
                          className="w-full h-32 object-cover rounded-lg"
                        />
                        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors rounded-lg"></div>
                        <div className="absolute bottom-2 left-2 right-2">
                          <p className="text-xs text-white bg-black/50 rounded px-2 py-1 truncate">
                            {originalName}
                          </p>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Prompt (para modos conservative y creative) */}
                  {(mode === "conservative" || mode === "creative") && (
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Descripción de la imagen *</Label>
                      <Textarea
                        placeholder="Describe la imagen para obtener mejores resultados..."
                        value={prompt}
                        onChange={(e) => setPrompt(e.target.value)}
                        className="min-h-[80px] resize-none"
                      />
                    </div>
                  )}

                  {/* Formato de salida */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium">Formato de Salida</Label>
                    <div className="grid grid-cols-1 gap-2">
                      {[
                        { value: "webp", label: "WebP", desc: "Recomendado" },
                        { value: "png", label: "PNG", desc: "Sin pérdida" },
                        { value: "jpeg", label: "JPEG", desc: "Menor tamaño" }
                      ].map((format) => (
                        <Button
                          key={format.value}
                          variant={outputFormat === format.value ? "default" : "outline"}
                          size="sm"
                          onClick={() => setOutputFormat(format.value as "jpeg" | "png" | "webp")}
                          className={`justify-start transition-all ${
                            outputFormat === format.value
                              ? "bg-indigo-600 hover:bg-indigo-700 text-white shadow-md"
                              : "hover:bg-indigo-50 hover:border-indigo-300"
                          }`}
                        >
                          <div className="text-left">
                            <div className="font-medium">{format.label}</div>
                            <div className="text-xs opacity-75">{format.desc}</div>
                          </div>
                          {outputFormat === format.value && (
                            <div className="ml-auto">
                              <div className="w-2 h-2 bg-white rounded-full"></div>
                            </div>
                          )}
                        </Button>
                      ))}
                    </div>
                  </div>

                  {/* Creatividad (para modo creative) */}
                  {mode === "creative" && (
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Nivel de Creatividad: {creativity?.[0] || 0.3}</Label>
                      <Slider
                        value={creativity || [0.3]}
                        onValueChange={(value) => setCreativity(value || [0.3])}
                        min={0.1}
                        max={0.5}
                        step={0.1}
                        className="w-full"
                      />
                      <div className="text-xs text-gray-500">
                        Valores más altos agregan más detalles durante la mejora
                      </div>
                    </div>
                  )}

                  {/* Estilo (para modo creative) */}
                  {mode === "creative" && stylePresets.length > 0 && (
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Estilo (opcional)</Label>
                      <Select value={stylePreset || "none"} onValueChange={(value) => setStylePreset(value === "none" ? "" : value)}>
                        <SelectTrigger>
                          <SelectValue placeholder="Seleccionar estilo" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="none">Sin estilo específico</SelectItem>
                          {stylePresets.map((preset) => (
                            <SelectItem key={preset.id} value={preset.id}>
                              {preset.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  )}

                  <Button
                    onClick={handleProcessImage}
                    disabled={isProcessing || !originalImage || ((mode === "conservative" || mode === "creative") && !prompt.trim())}
                    className="w-full bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700"
                  >
                    {isProcessing ? (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                        Mejorando...
                      </>
                    ) : (
                      <>
                        <Sparkles className="h-4 w-4 mr-2" />
                        Mejorar Imagen
                      </>
                    )}
                  </Button>

                  {/* Barra de progreso */}
                  {isProcessing && (
                    <div className="space-y-2">
                      <Progress value={progress} className="w-full" />
                      <p className="text-sm text-gray-500 text-center">
                        {progressMessage}
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </motion.div>
          {/* Área de Visualización */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
            className="lg:col-span-2"
          >
            <Tabs value={mainTab} onValueChange={(value) => setMainTab(value as "latest" | "saved")} className="w-full">
              <TabsList className="grid w-full grid-cols-2 mb-6">
                <TabsTrigger value="latest" className="flex items-center gap-2">
                  <Zap className="h-4 w-4" />
                  Última Generación
                </TabsTrigger>
                <TabsTrigger value="saved" className="flex items-center gap-2">
                  <Heart className="h-4 w-4" />
                  Guardados ({savedImages.length})
                </TabsTrigger>
              </TabsList>

              <TabsContent value="latest" className="space-y-6">
                {/* Imagen Mejorada */}
                {resultImage && (
                  <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                    <CardHeader className="pb-4">
                      <div className="flex items-center justify-between">
                        <CardTitle className="flex items-center gap-2">
                          <Sparkles className="h-5 w-5 text-indigo-600" />
                          Imagen Mejorada
                        </CardTitle>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={handleToggleFavorite}
                            className={currentImageSaved ? "text-red-600 border-red-200" : ""}
                          >
                            {currentImageSaved ? (
                              <Heart className="h-4 w-4 fill-current" />
                            ) : (
                              <HeartOff className="h-4 w-4" />
                            )}
                          </Button>
                          <Button variant="outline" size="sm" onClick={handleShare}>
                            <Share2 className="h-4 w-4" />
                          </Button>
                          <Button variant="outline" size="sm" onClick={handleDownload}>
                            <Download className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="relative group">
                        <img
                          src={resultImage}
                          alt="Imagen mejorada"
                          className="w-full rounded-lg shadow-lg"
                        />
                        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors rounded-lg"></div>
                      </div>

                      {/* Información de la imagen */}
                      <div className="mt-4 space-y-2">
                        <div className="flex items-start gap-2">
                          <Badge variant="secondary" className="mt-0.5">Modo</Badge>
                          <p className="text-sm text-gray-600 flex-1">{modes.find(m => m.id === mode)?.name}</p>
                        </div>

                        {prompt && (
                          <div className="flex items-start gap-2">
                            <Badge variant="outline" className="mt-0.5">Descripción</Badge>
                            <p className="text-xs text-gray-500 flex-1">{prompt}</p>
                          </div>
                        )}

                        <div className="flex items-center gap-2 text-xs text-gray-400">
                          <span>Formato: {outputFormat.toUpperCase()}</span>
                          <span>• Archivo: {originalName}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Estado vacío */}
                {!resultImage && (
                  <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                    <CardContent className="flex flex-col items-center justify-center py-16">
                      <div className="w-24 h-24 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-full flex items-center justify-center mb-6">
                        <Sparkles className="h-12 w-12 text-indigo-600" />
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">
                        ¡Mejora tu primera imagen!
                      </h3>
                      <p className="text-gray-600 text-center max-w-md mb-6">
                        Sube una imagen y selecciona el modo de mejora para comenzar. Nuestra IA transformará tu imagen con calidad profesional.
                      </p>
                      <div className="flex flex-wrap gap-2 justify-center">
                        <Badge variant="secondary">Rápido</Badge>
                        <Badge variant="secondary">Conservador</Badge>
                        <Badge variant="secondary">Creativo</Badge>
                        <Badge variant="secondary">Hasta 4K</Badge>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>

              <TabsContent value="saved" className="space-y-6">
                {savedImages.length === 0 ? (
                  <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                    <CardContent className="flex flex-col items-center justify-center py-16">
                      <div className="w-24 h-24 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-full flex items-center justify-center mb-6">
                        <Heart className="h-12 w-12 text-indigo-600" />
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">
                        No tienes imágenes guardadas
                      </h3>
                      <p className="text-gray-600 text-center max-w-md">
                        Las imágenes que marques como favoritas aparecerán aquí para acceso rápido.
                      </p>
                    </CardContent>
                  </Card>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {savedImages.map((savedImage) => (
                      <Card key={savedImage.id} className="shadow-xl border-0 bg-white/80 backdrop-blur-sm overflow-hidden">
                        <div className="relative group">
                          <img
                            src={savedImage.processedUrl}
                            alt="Imagen guardada"
                            className="w-full h-48 object-cover"
                          />
                          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors"></div>
                          <div className="absolute top-2 right-2">
                            <Badge variant="secondary" className="bg-black/50 text-white">
                              {savedImage.mode}
                            </Badge>
                          </div>
                        </div>

                        <CardContent className="p-4">
                          <div className="space-y-3">
                            <div className="flex items-start gap-2">
                              <Badge variant="secondary" className="mt-0.5 text-xs">
                                {savedImage.outputFormat.toUpperCase()}
                              </Badge>
                              <p className="text-sm text-gray-600 flex-1 line-clamp-2">
                                {savedImage.originalFilename}
                              </p>
                            </div>

                            <div className="text-xs text-gray-400">
                              {new Date(savedImage.timestamp).toLocaleDateString('es-ES', {
                                year: 'numeric',
                                month: 'short',
                                day: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit'
                              })}
                            </div>

                            {/* Botones de acción */}
                            <div className="flex gap-2">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => {
                                  // Cargar la imagen en la vista principal
                                  setResultImage(savedImage.processedUrl);
                                  setOriginalImagePreview(savedImage.originalUrl);
                                  setOriginalName(savedImage.originalFilename);

                                  // Cambiar a la pestaña "Última Generación"
                                  setMainTab("latest");

                                  toast({
                                    title: "🖼️ Imagen cargada",
                                    description: "Imagen cargada en la vista principal.",
                                  });
                                }}
                                className="flex-1"
                              >
                                <Eye className="h-3 w-3 mr-1" />
                                Ver
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={async () => {
                                  try {
                                    const response = await fetch(savedImage.processedUrl);
                                    const blob = await response.blob();
                                    const url = window.URL.createObjectURL(blob);
                                    const a = document.createElement("a");
                                    a.href = url;
                                    a.download = `enhanced-${savedImage.originalFilename}`;
                                    document.body.appendChild(a);
                                    a.click();
                                    window.URL.revokeObjectURL(url);
                                    document.body.removeChild(a);

                                    toast({
                                      title: "Descarga iniciada",
                                      description: "La imagen se está descargando",
                                    });
                                  } catch (error) {
                                    toast({
                                      title: "Error",
                                      description: "No se pudo descargar la imagen",
                                      variant: "destructive",
                                    });
                                  }
                                }}
                              >
                                <Download className="h-3 w-3 mr-1" />
                                Descargar
                              </Button>
                              <Button
                                size="sm"
                                variant="destructive"
                                onClick={() => {
                                  const filteredImages = savedImages.filter(img => img.id !== savedImage.id);
                                  setSavedImages(filteredImages);

                                  toast({
                                    title: "🗑️ Imagen eliminada",
                                    description: "La imagen ha sido eliminada de tus guardados.",
                                  });
                                }}
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </motion.div>
        </div>
      </div>
    </DashboardLayout>
  );
}
